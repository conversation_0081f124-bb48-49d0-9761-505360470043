---
interface Props {
  title: string;
  description: string;
  tags: string[];
  slug?: string;
  image?: string;
}

const { title, description, tags, slug, image } = Astro.props;

// Truncate long technology names for better display
const formatTag = (tag: string) => {
  if (tag.length > 12) {
    return tag.substring(0, 10) + '..';
  }
  return tag;
};

// Limit to maximum 6 tags and ensure we show only 2 rows
const displayTags = tags.slice(0, 6);
---

<article class="portfolio-card group card-interactive relative overflow-hidden min-h-[320px] flex flex-col" role="listitem">
  <!-- Gradient border accent -->
  <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 to-accent-500"></div>

  <!-- Optional project image placeholder -->
  {image && (
    <div class="relative h-48 bg-gradient-to-br from-primary-100 to-accent-100 dark:from-primary-900/20 dark:to-accent-900/20 overflow-hidden">
      <img
        src={image}
        alt={`${title} preview`}
        class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
        loading="lazy"
      />
      <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    </div>
  )}

  <!-- Card content with fixed sections -->
  <div class="portfolio-content p-6 flex flex-col flex-grow">
    <!-- Title section - fixed height -->
    <div class="mb-4 h-14 flex items-start">
      <h3 class="heading-sm text-secondary-800 dark:text-secondary-200 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300 line-clamp-2">
        {title}
      </h3>
    </div>

    <!-- Description section - fixed height with line clamping -->
    <div class="mb-6 flex-grow">
      <p class="text-secondary-600 dark:text-secondary-400 leading-relaxed text-sm line-clamp-3">
        {description}
      </p>
    </div>

    <!-- Technology tags section - fixed height for 2 rows -->
    <div class="mt-auto">
      <div class="flex flex-wrap gap-2 h-16 overflow-hidden">
        {displayTags.map((tag, index) => (
          <span
            class="tag inline-flex items-center px-3 py-1 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 text-xs font-medium rounded-full border border-primary-200 dark:border-primary-700 hover:bg-primary-100 dark:hover:bg-primary-900/40 transition-all duration-300 hover:scale-105"
            style={`animation-delay: ${index * 0.05}s;`}
          >
            {formatTag(tag)}
          </span>
        ))}
      </div>
    </div>
  </div>

  <!-- Hover overlay effect -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-accent-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
</article>

<style>
  /* Line clamping utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Enhanced hover effects */
  .portfolio-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
  }

  .portfolio-card:hover .tag {
    animation: tagFloat 0.3s ease-out forwards;
  }

  @keyframes tagFloat {
    0% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-2px);
    }
    100% {
      transform: translateY(0);
    }
  }

  /* Ensure consistent card heights */
  .portfolio-card {
    height: 320px;
  }

  /* Dark mode adjustments */
  .dark .portfolio-card {
    background: theme('colors.secondary.800');
    border-color: theme('colors.secondary.700');
  }

  .dark .portfolio-card:hover {
    border-color: theme('colors.primary.600');
  }
</style>