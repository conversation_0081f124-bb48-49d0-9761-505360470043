---
// Theme toggle component for dark/light mode switching
---

<button
  id="theme-toggle"
  class="theme-toggle relative p-3 rounded-2xl bg-gradient-to-br from-secondary-50 to-secondary-100 hover:from-secondary-100 hover:to-secondary-200 dark:from-secondary-800 dark:to-secondary-900 dark:hover:from-secondary-700 dark:hover:to-secondary-800 transition-all duration-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 shadow-lg hover:shadow-xl dark:shadow-secondary-900/50 overflow-hidden group"
  aria-label="Toggle theme mode"
  title="Toggle between light and dark mode"
  role="switch"
  aria-checked="false"
>
  <!-- Animated background ripple effect -->
  <div class="ripple-effect absolute inset-0 rounded-2xl opacity-0 bg-gradient-to-r from-primary-400/20 to-primary-600/20 transform scale-0 transition-all duration-700 ease-out"></div>

  <!-- Morphing icon container -->
  <div class="icon-container relative w-6 h-6 transform transition-all duration-500 ease-out group-hover:scale-110 group-active:scale-95">
    <!-- Sun icon with enhanced animations -->
    <svg
      class="sun-icon absolute inset-0 w-6 h-6 text-amber-500 dark:text-amber-400 transition-all duration-500 ease-out transform dark:rotate-180 dark:scale-0 dark:opacity-0"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <!-- Sun center -->
      <circle cx="12" cy="12" r="4" stroke-width="2" class="sun-center transition-all duration-500"/>
      <!-- Sun rays with staggered animation -->
      <g class="sun-rays">
        <path stroke-linecap="round" stroke-width="2" d="M12 3v1" class="ray ray-1"/>
        <path stroke-linecap="round" stroke-width="2" d="M12 20v1" class="ray ray-2"/>
        <path stroke-linecap="round" stroke-width="2" d="M21 12h-1" class="ray ray-3"/>
        <path stroke-linecap="round" stroke-width="2" d="M4 12H3" class="ray ray-4"/>
        <path stroke-linecap="round" stroke-width="2" d="M18.364 5.636l-.707.707" class="ray ray-5"/>
        <path stroke-linecap="round" stroke-width="2" d="M6.343 17.657l-.707.707" class="ray ray-6"/>
        <path stroke-linecap="round" stroke-width="2" d="M5.636 5.636l.707.707" class="ray ray-7"/>
        <path stroke-linecap="round" stroke-width="2" d="M17.657 17.657l.707.707" class="ray ray-8"/>
      </g>
    </svg>

    <!-- Moon icon with enhanced animations -->
    <svg
      class="moon-icon absolute inset-0 w-6 h-6 text-indigo-600 dark:text-indigo-400 transition-all duration-500 ease-out transform scale-0 opacity-0 dark:scale-100 dark:opacity-100 dark:rotate-0"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
        class="moon-crescent transition-all duration-500"
      ></path>
      <!-- Moon stars -->
      <g class="moon-stars opacity-0 dark:opacity-100 transition-opacity duration-700 delay-200">
        <circle cx="17" cy="7" r="0.5" fill="currentColor" class="star star-1"/>
        <circle cx="19" cy="9" r="0.3" fill="currentColor" class="star star-2"/>
        <circle cx="16" cy="10" r="0.4" fill="currentColor" class="star star-3"/>
      </g>
    </svg>
  </div>

  <!-- Glow effect -->
  <div class="glow-effect absolute inset-0 rounded-2xl opacity-0 bg-gradient-to-r from-primary-400/30 to-primary-600/30 blur-md transition-all duration-300 group-hover:opacity-100"></div>
</button>

<script>
  // Enhanced theme toggle functionality with modern animations
  function initThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle') as HTMLButtonElement;
    const html = document.documentElement;
    const rippleEffect = themeToggle?.querySelector('.ripple-effect') as HTMLElement;

    if (!themeToggle) return;

    // Check for saved theme preference, system preference, or default to light mode
    const savedTheme = localStorage.getItem('theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

    // Set initial theme with improved logic
    const shouldUseDark = savedTheme === 'dark' || (savedTheme === 'system' && systemPrefersDark) || (!savedTheme && systemPrefersDark);

    if (shouldUseDark) {
      html.classList.add('dark');
      themeToggle.setAttribute('aria-checked', 'true');
    } else {
      html.classList.remove('dark');
      themeToggle.setAttribute('aria-checked', 'false');
    }

    // Enhanced toggle theme function with animations
    function toggleTheme(event: Event) {
      const isDark = html.classList.contains('dark');

      // Trigger ripple effect animation
      triggerRippleEffect(event);

      // Add loading state
      themeToggle.style.pointerEvents = 'none';

      // Animate theme transition
      requestAnimationFrame(() => {
        if (isDark) {
          html.classList.remove('dark');
          localStorage.setItem('theme', 'light');
          themeToggle.setAttribute('aria-checked', 'false');
          announceThemeChange('light mode');
        } else {
          html.classList.add('dark');
          localStorage.setItem('theme', 'dark');
          themeToggle.setAttribute('aria-checked', 'true');
          announceThemeChange('dark mode');
        }

        // Re-enable button after animation
        setTimeout(() => {
          themeToggle.style.pointerEvents = 'auto';
        }, 500);

        // Dispatch custom event for other components
        window.dispatchEvent(new CustomEvent('theme-changed', {
          detail: {
            theme: isDark ? 'light' : 'dark',
            source: 'user',
            timestamp: Date.now()
          }
        }));
      });
    }

    // Ripple effect animation
    function triggerRippleEffect(event: Event) {
      if (!rippleEffect) return;

      rippleEffect.style.opacity = '1';
      rippleEffect.style.transform = 'scale(1.5)';

      setTimeout(() => {
        rippleEffect.style.opacity = '0';
        rippleEffect.style.transform = 'scale(0)';
      }, 700);
    }

    // Announce theme changes to screen readers
    function announceThemeChange(theme: string) {
      const announcement = document.createElement('div');
      announcement.setAttribute('aria-live', 'polite');
      announcement.setAttribute('aria-atomic', 'true');
      announcement.className = 'sr-only';
      announcement.textContent = `Switched to ${theme}`;
      document.body.appendChild(announcement);

      setTimeout(() => {
        document.body.removeChild(announcement);
      }, 1000);
    }

    // Enhanced event listeners
    themeToggle.addEventListener('click', toggleTheme);

    // Keyboard support
    themeToggle.addEventListener('keydown', (event: KeyboardEvent) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        toggleTheme(event);
      }
    });

    // Listen for system theme changes (improved responsiveness)
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      const savedTheme = localStorage.getItem('theme');
      if (!savedTheme || savedTheme === 'system') {
        if (e.matches) {
          html.classList.add('dark');
          themeToggle.setAttribute('aria-checked', 'true');
        } else {
          html.classList.remove('dark');
          themeToggle.setAttribute('aria-checked', 'false');
        }

        // Dispatch custom event for other components
        window.dispatchEvent(new CustomEvent('theme-changed', {
          detail: {
            theme: e.matches ? 'dark' : 'light',
            source: 'system',
            timestamp: Date.now()
          }
        }));
      }
    });
  }

  // Initialize on DOM content loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initThemeToggle);
  } else {
    initThemeToggle();
  }

  // Re-initialize on page navigation (for SPA-like behavior)
  document.addEventListener('astro:page-load', initThemeToggle);
</script>

<style>
  /* Modern theme toggle with enhanced animations */
  .theme-toggle {
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
  }

  .theme-toggle:hover {
    transform: scale(1.05) translateY(-1px);
    box-shadow:
      0 10px 25px rgba(0, 0, 0, 0.15),
      0 0 20px rgba(59, 130, 246, 0.3);
  }

  .theme-toggle:active {
    transform: scale(0.95);
    transition-duration: 0.1s;
  }

  .theme-toggle:focus-visible {
    outline: none;
    box-shadow:
      0 0 0 2px rgba(59, 130, 246, 0.5),
      0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* Icon container animations */
  .icon-container {
    transform-origin: center;
  }

  /* Enhanced sun icon animations */
  .sun-icon {
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    filter: drop-shadow(0 0 8px rgba(245, 158, 11, 0.5));
  }

  .sun-icon .sun-center {
    transform-origin: center;
    transition: all 0.5s ease-out;
  }

  .sun-icon .sun-rays {
    transform-origin: center;
  }

  .sun-icon .ray {
    transition: all 0.5s ease-out;
    transform-origin: center;
  }

  /* Staggered ray animations */
  .sun-icon .ray-1 { transition-delay: 0ms; }
  .sun-icon .ray-2 { transition-delay: 50ms; }
  .sun-icon .ray-3 { transition-delay: 100ms; }
  .sun-icon .ray-4 { transition-delay: 150ms; }
  .sun-icon .ray-5 { transition-delay: 200ms; }
  .sun-icon .ray-6 { transition-delay: 250ms; }
  .sun-icon .ray-7 { transition-delay: 300ms; }
  .sun-icon .ray-8 { transition-delay: 350ms; }

  /* Enhanced moon icon animations */
  .moon-icon {
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    filter: drop-shadow(0 0 8px rgba(99, 102, 241, 0.5));
  }

  .moon-icon .moon-crescent {
    transition: all 0.5s ease-out;
    transform-origin: center;
  }

  .moon-icon .moon-stars {
    transition: opacity 0.7s ease-out;
  }

  .moon-icon .star {
    transition: all 0.3s ease-out;
    transform-origin: center;
  }

  .moon-icon .star-1 { transition-delay: 0ms; }
  .moon-icon .star-2 { transition-delay: 100ms; }
  .moon-icon .star-3 { transition-delay: 200ms; }

  /* Ripple effect */
  .ripple-effect {
    transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
  }

  /* Glow effect */
  .glow-effect {
    transition: all 0.3s ease-out;
    transform: scale(1.1);
  }

  /* Hover animations for icons */
  .theme-toggle:hover .sun-icon .ray {
    transform: scale(1.1);
  }

  .theme-toggle:hover .moon-icon .star {
    transform: scale(1.2);
  }

  /* Active state animations */
  .theme-toggle:active .icon-container {
    transform: scale(0.9) rotate(15deg);
  }

  /* Dark mode specific adjustments */
  .dark .theme-toggle {
    border-color: rgba(255, 255, 255, 0.1);
  }

  .dark .theme-toggle:hover {
    box-shadow:
      0 10px 25px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(99, 102, 241, 0.4);
  }

  /* Accessibility: Respect reduced motion preference */
  @media (prefers-reduced-motion: reduce) {
    .theme-toggle,
    .sun-icon,
    .moon-icon,
    .ripple-effect,
    .glow-effect,
    .icon-container,
    .ray,
    .star {
      transition-duration: 0.01s !important;
      animation: none !important;
    }
  }

  /* Screen reader only class */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
</style>
