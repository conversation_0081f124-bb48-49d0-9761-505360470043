---
import Layout from '../layouts/Layout.astro';
import { getEntry } from 'astro:content';

const aboutData = await getEntry('about', 'profile');
const { title, sections } = aboutData.data;
---

<Layout title="About | Nob Hokleng | Software Developer & System Architect">
  <section class="about pt-32 pb-24 bg-white">
    <div class="container mx-auto px-5 max-w-4xl">
      <h1 class="text-4xl font-bold text-center mb-12 font-heading relative">
        {title}
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h1>
      
      <div class="space-y-8 text-lg leading-relaxed">
        {sections.map((section) => (
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-secondary mb-6 font-heading">
              {section.heading}
            </h2>
            
            {section.content && (
              <div class="mb-6">
                {section.content.split('\n\n').map((paragraph) => (
                  <p class="text-text mb-4">{paragraph}</p>
                ))}
              </div>
            )}
            
            {section.subsections && section.subsections.map((subsection) => (
              <div class="mt-6">
                {subsection.subheading && (
                  <h3 class="text-xl font-semibold text-primary mb-4 font-heading">
                    {subsection.subheading}
                  </h3>
                )}
                
                {subsection.items && (
                  <ul class="space-y-2 text-text">
                    {subsection.items.map((item) => (
                      <li class="flex items-center">
                        <span class="text-primary mr-3 font-bold">✓</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                )}
                
                {subsection.content && (
                  <p class="text-text">{subsection.content}</p>
                )}
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  </section>
</Layout> 
