---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Resume | Nob <PERSON> | Software Developer & System Architect">
  <section class="resume pt-32 pb-24 bg-light" role="region" aria-labelledby="resume-title">
    <div class="container mx-auto px-5 max-w-6xl">
      <h1 id="resume-title" class="text-4xl font-bold text-center mb-12 font-heading relative">
        Resume
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h1>
      
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-10">
        <div class="lg:col-span-2 space-y-8">
          <div class="resume-summary bg-white p-8 rounded-2xl shadow-lg">
            <h2 class="text-2xl font-bold text-secondary mb-6 font-heading">Professional Summary</h2>
            <p class="text-text leading-relaxed text-base">
              Software developer with experience in building scalable systems and modern web applications. Passionate about clean code, system architecture, and continuous learning. Experienced in full-stack development with a focus on backend technologies and DevOps practices.
            </p>
          </div>
          
          <div class="experience-areas bg-white p-8 rounded-2xl shadow-lg">
            <h2 class="text-2xl font-bold text-secondary mb-6 font-heading">Key Experience Areas</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="experience-item bg-light p-6 rounded-xl hover:shadow-md transition-all duration-300 hover:-translate-y-1">
                <h3 class="text-lg font-semibold text-primary mb-3 font-heading">Backend Development</h3>
                <p class="text-text text-sm leading-relaxed">API design, database optimization, microservices architecture</p>
              </div>
              <div class="experience-item bg-light p-6 rounded-xl hover:shadow-md transition-all duration-300 hover:-translate-y-1">
                <h3 class="text-lg font-semibold text-primary mb-3 font-heading">System Architecture</h3>
                <p class="text-text text-sm leading-relaxed">Scalable system design, performance optimization, cloud infrastructure</p>
              </div>
              <div class="experience-item bg-light p-6 rounded-xl hover:shadow-md transition-all duration-300 hover:-translate-y-1">
                <h3 class="text-lg font-semibold text-primary mb-3 font-heading">DevOps & Automation</h3>
                <p class="text-text text-sm leading-relaxed">CI/CD pipelines, containerization, infrastructure as code</p>
              </div>
              <div class="experience-item bg-light p-6 rounded-xl hover:shadow-md transition-all duration-300 hover:-translate-y-1">
                <h3 class="text-lg font-semibold text-primary mb-3 font-heading">Technical Leadership</h3>
                <p class="text-text text-sm leading-relaxed">Code review, mentoring, technical decision making</p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="resume-actions bg-white p-8 rounded-2xl shadow-lg h-fit">
          <h3 class="text-xl font-bold text-secondary mb-6 font-heading text-center">Download Resume</h3>
          <div class="text-center space-y-6">
            <p class="coming-soon bg-light border-2 border-dashed border-gray-200 p-6 rounded-xl text-gray-500 italic text-sm">
              Interactive resume experience and PDF download coming soon
            </p>
            <button type="button" class="btn-disabled w-full px-6 py-3 bg-gray-300 text-gray-500 rounded-lg font-semibold cursor-not-allowed opacity-60" disabled aria-label="Resume PDF download coming soon">
              Download PDF (Coming Soon)
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout> 