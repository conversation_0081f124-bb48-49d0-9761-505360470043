/**
 * Design Tokens System
 * Modern CSS custom properties for theme management
 * Following design token hierarchy: primitive → semantic → component
 */

/* ==========================================================================
   PRIMITIVE TOKENS (Base values)
   ========================================================================== */

:root {
  /* Color Primitives */
  --primitive-blue-50: #eff6ff;
  --primitive-blue-100: #dbeafe;
  --primitive-blue-200: #bfdbfe;
  --primitive-blue-300: #93c5fd;
  --primitive-blue-400: #60a5fa;
  --primitive-blue-500: #3b82f6;
  --primitive-blue-600: #2563eb;
  --primitive-blue-700: #1d4ed8;
  --primitive-blue-800: #1e40af;
  --primitive-blue-900: #1e3a8a;
  --primitive-blue-950: #172554;

  --primitive-slate-50: #f8fafc;
  --primitive-slate-100: #f1f5f9;
  --primitive-slate-200: #e2e8f0;
  --primitive-slate-300: #cbd5e1;
  --primitive-slate-400: #94a3b8;
  --primitive-slate-500: #64748b;
  --primitive-slate-600: #475569;
  --primitive-slate-700: #334155;
  --primitive-slate-800: #1e293b;
  --primitive-slate-900: #0f172a;
  --primitive-slate-950: #020617;

  --primitive-orange-50: #fff7ed;
  --primitive-orange-100: #ffedd5;
  --primitive-orange-200: #fed7aa;
  --primitive-orange-300: #fdba74;
  --primitive-orange-400: #fb923c;
  --primitive-orange-500: #f97316;
  --primitive-orange-600: #ea580c;
  --primitive-orange-700: #c2410c;
  --primitive-orange-800: #9a3412;
  --primitive-orange-900: #7c2d12;

  --primitive-green-50: #f0fdf4;
  --primitive-green-500: #22c55e;
  --primitive-green-600: #16a34a;

  --primitive-red-50: #fef2f2;
  --primitive-red-500: #ef4444;
  --primitive-red-600: #dc2626;

  --primitive-yellow-50: #fffbeb;
  --primitive-yellow-500: #f59e0b;
  --primitive-yellow-600: #d97706;

  /* Spacing Primitives */
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Typography Primitives */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Border Radius Primitives */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Shadow Primitives */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Animation Primitives */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --easing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ==========================================================================
   SEMANTIC TOKENS (Light Theme)
   ========================================================================== */

:root {
  /* Background Colors */
  --color-bg-primary: var(--primitive-slate-50);
  --color-bg-secondary: var(--primitive-slate-100);
  --color-bg-tertiary: var(--primitive-slate-200);
  --color-bg-inverse: var(--primitive-slate-900);

  /* Text Colors */
  --color-text-primary: var(--primitive-slate-900);
  --color-text-secondary: var(--primitive-slate-700);
  --color-text-tertiary: var(--primitive-slate-500);
  --color-text-inverse: var(--primitive-slate-50);
  --color-text-link: var(--primitive-blue-600);
  --color-text-link-hover: var(--primitive-blue-700);

  /* Border Colors */
  --color-border-primary: var(--primitive-slate-200);
  --color-border-secondary: var(--primitive-slate-300);
  --color-border-focus: var(--primitive-blue-500);

  /* Brand Colors */
  --color-brand-primary: var(--primitive-blue-600);
  --color-brand-primary-hover: var(--primitive-blue-700);
  --color-brand-secondary: var(--primitive-orange-500);
  --color-brand-secondary-hover: var(--primitive-orange-600);

  /* Status Colors */
  --color-success: var(--primitive-green-500);
  --color-success-bg: var(--primitive-green-50);
  --color-warning: var(--primitive-yellow-500);
  --color-warning-bg: var(--primitive-yellow-50);
  --color-error: var(--primitive-red-500);
  --color-error-bg: var(--primitive-red-50);

  /* Interactive Colors */
  --color-interactive-primary: var(--primitive-blue-600);
  --color-interactive-primary-hover: var(--primitive-blue-700);
  --color-interactive-secondary: var(--primitive-slate-200);
  --color-interactive-secondary-hover: var(--primitive-slate-300);

  /* Glassmorphism Effects */
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  --glass-backdrop-blur: blur(16px);
}

/* ==========================================================================
   SEMANTIC TOKENS (Dark Theme)
   ========================================================================== */

.dark {
  /* Background Colors */
  --color-bg-primary: var(--primitive-slate-900);
  --color-bg-secondary: var(--primitive-slate-800);
  --color-bg-tertiary: var(--primitive-slate-700);
  --color-bg-inverse: var(--primitive-slate-50);

  /* Text Colors */
  --color-text-primary: var(--primitive-slate-50);
  --color-text-secondary: var(--primitive-slate-300);
  --color-text-tertiary: var(--primitive-slate-400);
  --color-text-inverse: var(--primitive-slate-900);
  --color-text-link: var(--primitive-blue-400);
  --color-text-link-hover: var(--primitive-blue-300);

  /* Border Colors */
  --color-border-primary: var(--primitive-slate-700);
  --color-border-secondary: var(--primitive-slate-600);
  --color-border-focus: var(--primitive-blue-500);

  /* Brand Colors */
  --color-brand-primary: var(--primitive-blue-500);
  --color-brand-primary-hover: var(--primitive-blue-400);
  --color-brand-secondary: var(--primitive-orange-500);
  --color-brand-secondary-hover: var(--primitive-orange-400);

  /* Status Colors */
  --color-success: var(--primitive-green-500);
  --color-success-bg: rgba(34, 197, 94, 0.1);
  --color-warning: var(--primitive-yellow-500);
  --color-warning-bg: rgba(245, 158, 11, 0.1);
  --color-error: var(--primitive-red-500);
  --color-error-bg: rgba(239, 68, 68, 0.1);

  /* Interactive Colors */
  --color-interactive-primary: var(--primitive-blue-500);
  --color-interactive-primary-hover: var(--primitive-blue-400);
  --color-interactive-secondary: var(--primitive-slate-700);
  --color-interactive-secondary-hover: var(--primitive-slate-600);

  /* Glassmorphism Effects */
  --glass-bg: rgba(15, 23, 42, 0.8);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --glass-backdrop-blur: blur(16px);
}

/* ==========================================================================
   COMPONENT TOKENS
   ========================================================================== */

:root {
  /* Button Tokens */
  --button-padding-x: var(--space-4);
  --button-padding-y: var(--space-2);
  --button-border-radius: var(--radius-lg);
  --button-font-weight: var(--font-weight-medium);
  --button-transition: all var(--duration-normal) var(--easing-ease-out);

  /* Card Tokens */
  --card-padding: var(--space-6);
  --card-border-radius: var(--radius-xl);
  --card-shadow: var(--shadow-lg);
  --card-border: 1px solid var(--color-border-primary);

  /* Header Tokens */
  --header-height: 4rem;
  --header-padding-x: var(--space-6);
  --header-backdrop-blur: var(--glass-backdrop-blur);
  --header-bg: var(--glass-bg);
  --header-border: var(--glass-border);
  --header-shadow: var(--glass-shadow);

  /* Navigation Tokens */
  --nav-link-padding-x: var(--space-3);
  --nav-link-padding-y: var(--space-2);
  --nav-link-border-radius: var(--radius-md);
  --nav-link-transition: all var(--duration-normal) var(--easing-ease-out);
}

/* ==========================================================================
   UTILITY CLASSES
   ========================================================================== */

.theme-transition {
  transition: background-color var(--duration-normal) var(--easing-ease-out),
              color var(--duration-normal) var(--easing-ease-out),
              border-color var(--duration-normal) var(--easing-ease-out);
}

.glass-effect {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop-blur);
  -webkit-backdrop-filter: var(--glass-backdrop-blur);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

/* ==========================================================================
   ACCESSIBILITY & MOTION PREFERENCES
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
  :root {
    --duration-fast: 0ms;
    --duration-normal: 0ms;
    --duration-slow: 0ms;
  }
  
  .theme-transition {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-border-primary: var(--primitive-slate-900);
    --color-border-secondary: var(--primitive-slate-800);
  }
  
  .dark {
    --color-border-primary: var(--primitive-slate-100);
    --color-border-secondary: var(--primitive-slate-200);
  }
}
