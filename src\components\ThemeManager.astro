---
// Enhanced Theme Manager with modern best practices
// Provides system sync, smooth transitions, and accessibility features
---

<script>
  // Enhanced theme management with 2024 best practices
  class ThemeManager {
    private html = document.documentElement;
    private storageKey = 'theme-preference';
    
    constructor() {
      this.init();
      this.setupEventListeners();
    }
    
    init() {
      // Get saved preference or default to system
      const savedTheme = localStorage.getItem(this.storageKey) || 'system';
      this.applyTheme(savedTheme);
    }
    
    applyTheme(theme: string) {
      // Remove any existing theme classes
      this.html.classList.remove('dark', 'light');
      
      let actualTheme = theme;
      
      // Handle system theme
      if (theme === 'system') {
        actualTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      }
      
      // Apply theme
      if (actualTheme === 'dark') {
        this.html.classList.add('dark');
      } else {
        this.html.classList.add('light');
      }
      
      // Store preference
      localStorage.setItem(this.storageKey, theme);
      
      // Dispatch custom event
      this.dispatchThemeChange(actualTheme, theme);
    }
    
    toggleTheme() {
      const currentTheme = localStorage.getItem(this.storageKey) || 'system';
      const isDark = this.html.classList.contains('dark');
      
      // Cycle through: light -> dark -> system
      let newTheme: string;
      if (currentTheme === 'light') {
        newTheme = 'dark';
      } else if (currentTheme === 'dark') {
        newTheme = 'system';
      } else {
        newTheme = 'light';
      }
      
      this.applyTheme(newTheme);
    }
    
    setTheme(theme: string) {
      this.applyTheme(theme);
    }
    
    getCurrentTheme() {
      return localStorage.getItem(this.storageKey) || 'system';
    }
    
    private dispatchThemeChange(actualTheme: string, preference: string) {
      window.dispatchEvent(new CustomEvent('theme-changed', {
        detail: { 
          theme: actualTheme, 
          preference,
          timestamp: Date.now()
        }
      }));
    }
    
    private setupEventListeners() {
      // Listen for system theme changes
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        const savedTheme = localStorage.getItem(this.storageKey);
        if (!savedTheme || savedTheme === 'system') {
          this.applyTheme('system');
        }
      });
      
      // Listen for reduced motion preference
      window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
        this.html.style.setProperty('--theme-transition-duration', e.matches ? '0s' : '0.4s');
      });
    }
  }
  
  // Initialize theme manager
  let themeManager: ThemeManager;
  
  function initThemeManager() {
    if (!themeManager) {
      themeManager = new ThemeManager();
    }
  }
  
  // Make theme manager globally available
  (window as any).themeManager = themeManager;
  
  // Initialize on DOM ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initThemeManager);
  } else {
    initThemeManager();
  }
  
  // Re-initialize on Astro page navigation
  document.addEventListener('astro:page-load', initThemeManager);
</script>

<style>
  /* Theme transition styles */
  :root {
    --theme-transition-duration: 0.4s;
  }
  
  @media (prefers-reduced-motion: reduce) {
    :root {
      --theme-transition-duration: 0s;
    }
  }
  
  * {
    transition-duration: var(--theme-transition-duration);
  }
</style>
