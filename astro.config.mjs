import { defineConfig } from 'astro/config';
import tailwind from "@astrojs/tailwind";
import mdx from "@astrojs/mdx";

// https://astro.build/config
export default defineConfig({
  site: process.env.VITE_SITE_URL || 'http://localhost:4321',
  integrations: [
    tailwind({
      config: {
        applyBaseStyles: true
      }
    }),
    mdx()
  ],
  image: {
    // Enable image optimization
    service: {
      entrypoint: 'astro/assets/services/sharp'
    },
    // Configure image formats and quality
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.githubusercontent.com'
      },
      {
        protocol: 'https',
        hostname: '*.vercel.app'
      },
      {
        protocol: 'https',
        hostname: '*.netlify.app'
      }
    ]
  },
  build: {
    assetsPrefix: process.env.VITE_ASSETS_PREFIX || '/',
    inlineStylesheets: 'auto', // Inline critical CSS automatically
    rollupOptions: {
      output: {
        entryFileNames: '[name].[hash].js',
        chunkFileNames: 'chunks/[name].[hash].js',
        assetFileNames: 'assets/[name].[hash][extname]',
        // Optimize chunk splitting for better caching
        manualChunks: {
          vendor: ['astro'],
        }
      },
    },
  },
  // Performance optimizations
  vite: {
    build: {
      // Enable CSS code splitting
      cssCodeSplit: true,
      // Optimize dependencies
      rollupOptions: {
        external: [],
        output: {
          // Optimize chunk sizes
          chunkSizeWarningLimit: 1000,
        }
      }
    },
    // Optimize dev server
    server: {
      fs: {
        strict: false
      }
    }
  },
});