---
import Layout from '../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import ProjectCard from '../../components/ProjectCard.astro';

const projects = await getCollection('portfolio');
---

<Layout title="Portfolio | Nob Hokleng | Software Developer & System Architect">
  <section class="portfolio pt-32 pb-24 bg-light" role="region" aria-labelledby="portfolio-title">
    <div class="container mx-auto px-5 max-w-6xl">
      <h1 id="portfolio-title" class="text-4xl font-bold text-center mb-12 font-heading relative">
        Portfolio
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h1>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" role="list">
        {projects.map(project => (
          <div role="listitem">
            <a href={`/portfolio/${project.slug}`} class="block">
              <ProjectCard
                title={project.data.title}
                description={project.data.problem}
                tags={project.data.technologies.slice(0, 3)}
              />
            </a>
          </div>
        ))}
      </div>
    </div>
  </section>
</Layout> 