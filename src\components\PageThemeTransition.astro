---
// Page-wide theme transition effects component
// Handles advanced animations during theme switching
---

<script>
  // Enhanced page theme transition manager
  class PageThemeTransitionManager {
    private isTransitioning = false;
    private transitionElements: HTMLElement[] = [];
    
    constructor() {
      this.init();
    }
    
    init() {
      // Listen for theme change events
      window.addEventListener('theme-changed', (event: CustomEvent) => {
        if (event.detail.source === 'user') {
          this.handleThemeTransition(event.detail);
        }
      });
      
      // Mark elements for staggered animations
      this.setupStaggeredElements();
      
      // Setup intersection observer for scroll-based animations
      this.setupScrollAnimations();
    }
    
    handleThemeTransition(detail: any) {
      if (this.isTransitioning) return;
      
      this.isTransitioning = true;
      
      // Add transition class to body
      document.body.classList.add('theme-transitioning');
      
      // Trigger staggered animations
      this.triggerStaggeredAnimations();
      
      // Animate specific page elements
      this.animatePageElements(detail.theme === 'dark');
      
      // Clean up after transition
      setTimeout(() => {
        this.cleanupTransition();
      }, 800);
    }
    
    setupStaggeredElements() {
      // Find elements that should animate in sequence
      const staggerElements = document.querySelectorAll(
        'header, main > section, .project-card, .skill-item, footer'
      );
      
      staggerElements.forEach((element, index) => {
        (element as HTMLElement).style.setProperty('--stagger-delay', index.toString());
        element.classList.add('theme-transition-stagger');
      });
    }
    
    triggerStaggeredAnimations() {
      const staggerElements = document.querySelectorAll('.theme-transition-stagger');
      
      staggerElements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('transitioning');
          
          // Remove class after animation
          setTimeout(() => {
            element.classList.remove('transitioning');
          }, 300);
        }, index * 50);
      });
    }
    
    animatePageElements(isDark: boolean) {
      // Animate hero section background
      this.animateHeroBackground(isDark);
      
      // Animate navigation elements
      this.animateNavigation(isDark);
      
      // Animate cards and content blocks
      this.animateContentBlocks(isDark);
      
      // Animate floating elements
      this.animateFloatingElements(isDark);
    }
    
    animateHeroBackground(isDark: boolean) {
      const hero = document.querySelector('.hero');
      if (hero) {
        const bgElements = hero.querySelectorAll('[class*="bg-gradient"], [class*="absolute"]');
        bgElements.forEach((element, index) => {
          setTimeout(() => {
            (element as HTMLElement).style.transform = 'scale(1.05)';
            setTimeout(() => {
              (element as HTMLElement).style.transform = 'scale(1)';
            }, 200);
          }, index * 100);
        });
      }
    }
    
    animateNavigation(isDark: boolean) {
      const nav = document.querySelector('nav');
      if (nav) {
        const navItems = nav.querySelectorAll('a, button');
        navItems.forEach((item, index) => {
          setTimeout(() => {
            item.classList.add('theme-transition-fade', 'transitioning');
            setTimeout(() => {
              item.classList.remove('transitioning');
            }, 200);
          }, index * 30);
        });
      }
    }
    
    animateContentBlocks(isDark: boolean) {
      const cards = document.querySelectorAll('.project-card, .skill-card, .card');
      cards.forEach((card, index) => {
        setTimeout(() => {
          (card as HTMLElement).style.transform = 'translateY(-5px)';
          (card as HTMLElement).style.boxShadow = isDark 
            ? '0 10px 30px rgba(0, 0, 0, 0.3)' 
            : '0 10px 30px rgba(0, 0, 0, 0.1)';
          
          setTimeout(() => {
            (card as HTMLElement).style.transform = 'translateY(0)';
          }, 300);
        }, index * 80);
      });
    }
    
    animateFloatingElements(isDark: boolean) {
      const floatingElements = document.querySelectorAll('[class*="animate-float"], [class*="floating"]');
      floatingElements.forEach((element, index) => {
        setTimeout(() => {
          (element as HTMLElement).style.animationPlayState = 'paused';
          setTimeout(() => {
            (element as HTMLElement).style.animationPlayState = 'running';
          }, 200);
        }, index * 100);
      });
    }
    
    setupScrollAnimations() {
      // Enhanced scroll animations that respect theme changes
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };
      
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !this.isTransitioning) {
            entry.target.classList.add('visible');
          }
        });
      }, observerOptions);
      
      // Observe elements that should animate on scroll
      const animateElements = document.querySelectorAll('.animate-on-scroll');
      animateElements.forEach(el => observer.observe(el));
    }
    
    cleanupTransition() {
      this.isTransitioning = false;
      document.body.classList.remove('theme-transitioning');
      
      // Reset any temporary styles
      const staggerElements = document.querySelectorAll('.theme-transition-stagger');
      staggerElements.forEach(element => {
        element.classList.remove('transitioning');
      });
      
      const fadeElements = document.querySelectorAll('.theme-transition-fade');
      fadeElements.forEach(element => {
        element.classList.remove('transitioning');
      });
    }
  }
  
  // Initialize the page theme transition manager
  let pageTransitionManager: PageThemeTransitionManager;
  
  function initPageTransitions() {
    if (!pageTransitionManager) {
      pageTransitionManager = new PageThemeTransitionManager();
    }
  }
  
  // Initialize on DOM ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPageTransitions);
  } else {
    initPageTransitions();
  }
  
  // Re-initialize on Astro page navigation
  document.addEventListener('astro:page-load', initPageTransitions);
</script>

<style>
  /* Enhanced page transition styles */
  .theme-transitioning {
    overflow-x: hidden;
  }
  
  .theme-transition-stagger.transitioning {
    transform: translateY(-2px);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .theme-transition-fade.transitioning {
    opacity: 0.8;
    transform: scale(0.98);
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
  }
  
  /* Smooth transitions for all interactive elements */
  a, button, .card, .project-card {
    transition: 
      transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      background-color var(--theme-transition-duration) var(--theme-transition-easing),
      color var(--theme-transition-duration) var(--theme-transition-easing);
  }
  
  /* Enhanced scroll animations */
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: 
      opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1),
      transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Floating element animations */
  @keyframes enhanced-float {
    0%, 100% { 
      transform: translateY(0px) rotate(0deg); 
    }
    50% { 
      transform: translateY(-10px) rotate(1deg); 
    }
  }
  
  .animate-float {
    animation: enhanced-float 3s ease-in-out infinite;
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .theme-transition-stagger,
    .theme-transition-fade,
    .animate-on-scroll,
    .animate-float {
      transition-duration: 0.01s !important;
      animation: none !important;
    }
  }
</style>
