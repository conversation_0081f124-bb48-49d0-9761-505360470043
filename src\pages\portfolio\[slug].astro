---
import Layout from '../../layouts/Layout.astro';
import { getCollection, type CollectionEntry } from 'astro:content';

export async function getStaticPaths() {
  const projects = await getCollection('portfolio');
  return projects.map(project => ({
    params: { slug: project.slug },
    props: { project },
  }));
}

interface Props {
  project: CollectionEntry<'portfolio'>;
}

const { project } = Astro.props;
const { Content } = await project.render();
---

<Layout title={project.data.title + ' | Portfolio | Nob Hokleng'}>
  <article class="pt-32 pb-24 bg-white">
    <div class="container mx-auto px-5 max-w-4xl">
      <!-- Project Header -->
      <header class="mb-12">
        <div class="mb-6">
          <a href="/portfolio" class="inline-flex items-center text-primary font-medium hover:text-secondary transition-colors">
            <i class="fas fa-arrow-left mr-2"></i> Back to Portfolio
          </a>
        </div>
        
        <h1 class="text-4xl md:text-5xl font-bold text-secondary mb-6 font-heading">{project.data.title}</h1>
        
        <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-8">
          <span class="flex items-center">
            <i class="fas fa-calendar mr-2"></i>
            {project.data.publishDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
          </span>
          <span class="flex items-center">
            <i class="fas fa-user mr-2"></i>
            {project.data.role}
          </span>
        </div>

        <div class="flex flex-wrap gap-2 mb-8">
          {project.data.technologies.map((tech) => (
            <span class="px-3 py-1 bg-gradient-to-br from-primary/10 to-secondary/10 text-primary text-sm font-medium rounded-full border border-primary/20">
              {tech}
            </span>
          ))}
        </div>
      </header>

      <!-- Project Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <div class="bg-light p-6 rounded-2xl">
          <h3 class="text-lg font-semibold text-secondary mb-3 font-heading flex items-center">
            <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
            Problem
          </h3>
          <p class="text-text text-sm leading-relaxed">{project.data.problem}</p>
        </div>
        
        <div class="bg-light p-6 rounded-2xl">
          <h3 class="text-lg font-semibold text-secondary mb-3 font-heading flex items-center">
            <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
            Solution
          </h3>
          <p class="text-text text-sm leading-relaxed">{project.data.solution}</p>
        </div>
        
        <div class="bg-light p-6 rounded-2xl">
          <h3 class="text-lg font-semibold text-secondary mb-3 font-heading flex items-center">
            <i class="fas fa-chart-line text-green-500 mr-2"></i>
            Results
          </h3>
          <p class="text-text text-sm leading-relaxed">{project.data.results}</p>
        </div>
      </div>

      <!-- Project Links -->
      {(project.data.repoUrl || project.data.liveUrl) && (
        <div class="flex flex-wrap gap-4 mb-12">
          {project.data.repoUrl && (
            <a href={project.data.repoUrl} target="_blank" rel="noopener noreferrer" 
               class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <i class="fab fa-github"></i>
              View Repository
            </a>
          )}
          {project.data.liveUrl && (
            <a href={project.data.liveUrl} target="_blank" rel="noopener noreferrer"
               class="inline-flex items-center gap-2 px-6 py-3 border-2 border-primary text-primary rounded-lg font-semibold hover:bg-primary/5 transition-all duration-300 hover:-translate-y-1">
              <i class="fas fa-external-link-alt"></i>
              Live Demo
            </a>
          )}
        </div>
      )}

      <!-- Project Content -->
      <div class="prose prose-lg max-w-none prose-headings:font-heading prose-headings:text-secondary prose-a:text-primary prose-strong:text-secondary">
        <Content />
      </div>

      <!-- Navigation -->
      <div class="mt-16 pt-8 border-t border-gray-200">
        <div class="flex justify-center">
          <a href="/portfolio" class="inline-flex items-center gap-2 px-6 py-3 bg-light text-secondary rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300">
            <i class="fas fa-arrow-left"></i>
            Back to Portfolio
          </a>
        </div>
      </div>
    </div>
  </article>
</Layout> 